import React, { useState, useEffect } from 'react';
import { Drawer, Form, Button, InputNumber, message, Divider } from 'antd';
import { SettingOutlined } from '@ant-design/icons';

interface PanelConfigDrawerProps {
  open: boolean;
  onClose: () => void;
  panelType: string;
  panelId: string;
  configuration?: any;
  onConfigurationSave: (panelId: string, config: any) => void;
}

interface XbarRbarConfig {
  subgroupSize: number;
}

const PanelConfigDrawer: React.FC<PanelConfigDrawerProps> = ({
  open,
  onClose,
  panelType,
  panelId,
  configuration,
  onConfigurationSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Initialize form with existing configuration
  useEffect(() => {
    if (open && configuration) {
      form.setFieldsValue(configuration);
    }
  }, [open, configuration, form]);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // Save configuration
      onConfigurationSave(panelId, values);
      message.success('Configuration saved successfully!');
      onClose();
    } catch (error) {
      console.error('Error saving configuration:', error);
      message.error('Failed to save configuration');
    } finally {
      setLoading(false);
    }
  };

  // Handle drawer close
  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  // Render configuration content based on panel type
  const renderConfigurationContent = () => {
    switch (panelType) {
      case 'XbarRbarPanel':
        return (
          <div>
            <h3 className="text-lg font-medium mb-4">X̄-R Chart Configuration</h3>
            <Form.Item
              name="subgroupSize"
              label="Subgroup Size"
              rules={[
                { required: true, message: 'Please enter subgroup size' },
                { type: 'number', min: 2, max: 25, message: 'Subgroup size must be between 2 and 25' }
              ]}
              tooltip="Number of consecutive data points to group together for control chart calculations"
            >
              <InputNumber
                min={2}
                max={25}
                placeholder="Enter subgroup size (e.g., 5)"
                style={{ width: '100%' }}
              />
            </Form.Item>
            
            <div className="text-sm text-gray-600 mt-2">
              <p><strong>Note:</strong> Subgroups are created by taking consecutive data points from your dataset.</p>
              <p>For example, with subgroup size 5:</p>
              <ul className="list-disc list-inside mt-1">
                <li>Subgroup 1: Data points 1-5</li>
                <li>Subgroup 2: Data points 6-10</li>
                <li>Subgroup 3: Data points 11-15</li>
                <li>And so on...</li>
              </ul>
            </div>
          </div>
        );
      
      default:
        return (
          <div>
            <p>No configuration options available for this panel type.</p>
          </div>
        );
    }
  };

  return (
    <Drawer
      title={
        <div className="flex items-center gap-2">
          <SettingOutlined />
          <span>Panel Configuration</span>
        </div>
      }
      open={open}
      onClose={handleClose}
      width={400}
      footer={
        <div className="flex justify-end gap-2">
          <Button onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            type="primary" 
            onClick={handleSubmit}
            loading={loading}
          >
            Save Configuration
          </Button>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          subgroupSize: 5 // Default subgroup size
        }}
      >
        {renderConfigurationContent()}
      </Form>
    </Drawer>
  );
};

export default PanelConfigDrawer;
