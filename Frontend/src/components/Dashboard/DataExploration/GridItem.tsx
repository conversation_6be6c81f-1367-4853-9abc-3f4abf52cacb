import React, { useState, useRef } from 'react';
import { ComponentType, GridItemData, ColumnSelection, DateFilter } from './types';
import { PanelFilter } from './FilterTypes';
import TimeSeriesPanel from './BatchExploration/panels/TimeSeriesPanel';
import OverviewPanel from './BatchExploration/panels/OverviewPanel';
import HistogramPanel from './BatchExploration/panels/HistogramPanel';
import DataTablePanel from './BatchExploration/panels/DataTablePanel';
import ScatterPlotPanel from './BatchExploration/panels/ScatterPlotPanel';
import XbarRbarPanel from './BatchExploration/panels/XbarRbarPanel';
import { CloseOutlined, ExpandOutlined } from '@ant-design/icons';
import PanelOptionsMenu from './PanelOptionsMenu';
import FullScreenModal from './FullScreenModal';

interface GridItemProps {
  file_id: string;
  data: GridItemData;
  fileData: any;
  filteredData?: any; // Pre-filtered data or panel-specific data
  isLoading?: boolean; // Loading state for exploration mode
  errorMessage?: string | null; // Error message for exploration mode
  isExplorationMode?: boolean; // Flag to determine behavior
  onRemove: (id: string) => void;

  // Selection and filter props
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;

  // Panel-specific filters
  panelFilters?: PanelFilter[];
  conditionalFilters?: PanelFilter[];

  // Target variable for scatter plot
  targetVariable?: string;

  // Selection and filter handlers
  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string , data?:any) => void;

  // Filter management handlers
  onAddFilter?: (filter: PanelFilter) => void;
  onRemoveFilter?: (filterId: string, panelId: string) => void;
  onClearAllFilters?: () => void;

  // Layout information for saving
  layout?: { x: number, y: number, w: number, h: number };
}

const GridItem: React.FC<GridItemProps> = ({
  data,
  fileData,
  filteredData,
  isLoading = false,
  errorMessage = null,
  isExplorationMode = false,
  file_id,
  onRemove,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = [],
  conditionalFilters = [],
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
  onAddFilter,
  onRemoveFilter,
  onClearAllFilters,
  layout,
  targetVariable
}) => {
  // State for panel display
  const [isFullScreen, setIsFullScreen] = useState(false);
  const panelContentRef = useRef<HTMLDivElement>(null);

  const effectiveColumnSelection = selectedColumns;

  const renderComponent = (isFullScreenMode = false) => {
    // Show error message for exploration mode
    if (isExplorationMode && errorMessage) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-2">⚠️</div>
            <p className="text-red-600 font-medium">Failed to load data</p>
            <p className="text-gray-500 text-sm mt-1">{errorMessage}</p>
          </div>
        </div>
      );
    }

    // Show loading indicator for exploration mode
    if (isExplorationMode && isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-gray-500">
              {filteredData ? 'Loading filtered data...' : 'Loading data...'}
            </p>
          </div>
        </div>
      );
    }

    switch (data.type) {
      case ComponentType.TimeSeriesPanel:
        return (
          <TimeSeriesPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            conditionalFilters={conditionalFilters}
            onZoomSelection={onZoomSelection}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
            onAddFilter={onAddFilter}
            isFullScreen={isFullScreenMode}
            onRemoveFilter={onRemoveFilter ? (filterId: string, panelId: string) => onRemoveFilter(filterId, panelId) : undefined}
            onClearAllFilters={onClearAllFilters}
          />
        );
      case ComponentType.OverviewPanel:
        return (
          <OverviewPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
          />
        );
      case ComponentType.HistogramPanel:
        return (
          <HistogramPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
          />
        );
      case ComponentType.DataTablePanel:
        return (
          <DataTablePanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            conditionalFilters={conditionalFilters}
            onDateFilterChange={onDateFilterChange}
            onAddFilter={onAddFilter}
            onRemoveFilter={onRemoveFilter ? (filterId: string) => onRemoveFilter(filterId, data.id) : undefined}
          />
        );
      case ComponentType.ScatterPlotPanel:
        return (
          <ScatterPlotPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
            targetVariable={targetVariable}
          />
        );
      case ComponentType.XbarRbarPanel:
        return (
          <XbarRbarPanel
            data={filteredData}
            filteredData={filteredData}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
          />
        );
      default:
        return <div>Unknown panel type</div>;
    }
  };

  // Get panel configuration for saving
  const getPanelConfiguration = () => {
    return {
      selectedColumns: effectiveColumnSelection,
      dateFilter: dateFilter,
      panelFilters: panelFilters,
      conditionalFilters: conditionalFilters,
      fileId: file_id
    };
  };

  // Get applied filters for saving
  const getAppliedFilters = () => {
    const filters = {
      selectedColumns: effectiveColumnSelection,
      dateFilter: dateFilter,
      panelFilters: panelFilters,
      conditionalFilters: conditionalFilters,
      valueRangeFilters: [] // Add if you have value range filters
    };

    console.log('GridItem - Getting applied filters for panel:', data.id, filters);
    return filters;
  };

  const handleRemove = () => {
    onRemove(data.id);
  };

  return (
    <>
      <div className="grid-item">
        <div className="grid-item-header">
          <div className="grid-item-title drag-handle">{data.title}</div>
          <div className="grid-item-controls no-drag">
            <button
              className="grid-item-control"
              title="Expand panel"
              onClick={() => setIsFullScreen(true)}
            >
              <ExpandOutlined />
            </button>
            <PanelOptionsMenu
              panelType={data.type}
              panelRef={panelContentRef}
              panelTitle={data.title}
              configuration={getPanelConfiguration()}
              fileId={file_id}
              panelId={data.id}
              layout={layout}
              onRemove={onRemove}
              appliedFilters={getAppliedFilters()}
            />
            <button
              className="grid-item-control"
              title="Remove from view (temporary)"
              onClick={handleRemove}
            >
              <CloseOutlined />
            </button>
          </div>
        </div>
        <div className="grid-item-content no-drag"  ref={panelContentRef}>{renderComponent(false)}</div>
      </div>

      {/* Full Screen Modal */}
      <FullScreenModal
        isOpen={isFullScreen}
        onClose={() => setIsFullScreen(false)}
        title={data.title}
      >
        <div className="full-screen-panel-content" style={{ width: '100%', height: '100%' }}>
          {/* Create a new instance of the component for full screen view */}
          {renderComponent(true)}
        </div>
      </FullScreenModal>
    </>
  );
};

export default GridItem;
