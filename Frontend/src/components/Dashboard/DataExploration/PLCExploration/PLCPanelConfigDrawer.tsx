import React, { useState, useEffect } from 'react';
import {
  Drawer,
  Form,
  Button,
  DatePicker,
  TimePicker,
  Select,
  Checkbox,
  Typography,
  message
} from 'antd';
import { CloseOutlined, SettingOutlined } from '@ant-design/icons';
import {
  PLCPanelConfigDrawerProps,
  PLCPanelConfiguration
} from './types/PLCTypes';
import { getAvailableColumns, getAllColumns, getGroupingColumns, generateMockPLCData } from './data/mockPLCData';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

const PLCPanelConfigDrawer: React.FC<PLCPanelConfigDrawerProps> = ({
  open,
  onClose,
  panelId,
  configuration,
  availableFeatures = [],
  currentlyDisplayedColumns = [],
  onConfigurationSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [windowMode, setWindowMode] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<string>('None');
  const [selectedTargets, setSelectedTargets] = useState<string[]>([]);
  const [windowLoading, setWindowLoading] = useState(false);

  // Handle Y-axis changes immediately (without submit)
  const handleYAxisChange = (selectedYAxis: string[]) => {
    if (!isDataLoaded || !panelId) return;

    const yAxisColumns = selectedYAxis && selectedYAxis.length > 0 ? selectedYAxis : ['VatPH'];
    const selectedColumns = {
      indices: yAxisColumns.map((col: string) => availableColumns.indexOf(col)).filter((idx: number) => idx !== -1),
      headers: yAxisColumns
    };

    const updatedConfiguration: PLCPanelConfiguration = {
      ...configuration,
      basic: {
        ...configuration?.basic,
        selectedColumns: selectedColumns,
      },
      title: `Time Series - ${yAxisColumns.join(', ')}`,
      lastModified: new Date().toISOString(),
    };

    onConfigurationSave(panelId, updatedConfiguration);
  };

  // Handle X-axis changes immediately (without submit)
  const handleXAxisChange = (selectedXAxis: string) => {
    if (!isDataLoaded || !panelId) return;

    const updatedConfiguration: PLCPanelConfiguration = {
      ...configuration,
      basic: {
        ...configuration?.basic,
        xAxisColumn: selectedXAxis || 'DateTime',
      },
      lastModified: new Date().toISOString(),
    };

    onConfigurationSave(panelId, updatedConfiguration);
  };

  // Handle grouping changes immediately (without submit)
  const handleGroupChange = (selectedGroup: string) => {
    if (!isDataLoaded || !panelId) return;

    const updatedConfiguration: PLCPanelConfiguration = {
      ...configuration,
      basic: {
        ...configuration?.basic,
        group: selectedGroup || 'None',
      },
      lastModified: new Date().toISOString(),
    };

    onConfigurationSave(panelId, updatedConfiguration);
  };

  // Initialize form with configuration data
  useEffect(() => {
    if (open && configuration) {
      // Set state variables to match configuration
      setSelectedGroup(configuration.basic.group || 'None');
      setWindowMode(configuration.advanced.windowMode || false);

      // Set window configuration targets if they exist
      if (configuration.advanced.windowConfig?.target) {
        setSelectedTargets(configuration.advanced.windowConfig.target);
      }

      form.setFieldsValue({
        startDate: configuration.dataRange.startDate ? dayjs(configuration.dataRange.startDate) : null,
        endDate: configuration.dataRange.endDate ? dayjs(configuration.dataRange.endDate) : null,
        startTime: configuration.dataRange.startTime ? dayjs(configuration.dataRange.startTime, 'HH:mm') : null,
        endTime: configuration.dataRange.endTime ? dayjs(configuration.dataRange.endTime, 'HH:mm') : null,
        xAxisColumn: configuration.basic.xAxisColumn || 'DateTime',
        yAxis: configuration.basic.selectedColumns?.headers || [],
        group: configuration.basic.group || 'None',
        windowMode: configuration.advanced.windowMode || false,
        target: configuration.advanced.windowConfig?.target || [],
        pre: configuration.advanced.windowConfig?.pre || undefined,
        post: configuration.advanced.windowConfig?.post || undefined,
      });
    } else if (open) {
      // Reset state variables for new configuration
      setSelectedGroup('None');
      setWindowMode(false);

      const defaultYAxis = currentlyDisplayedColumns.length > 0 ? currentlyDisplayedColumns : ['VatPH'];

      form.setFieldsValue({
        startDate: dayjs('2024-01-01'),
        endDate: dayjs('2024-01-02'),
        startTime: dayjs('00:00', 'HH:mm'),
        endTime: dayjs('23:59', 'HH:mm'),
        xAxisColumn: 'DateTime',
        yAxis: defaultYAxis,
        group: 'None',
        windowMode: false,
      });
    }
  }, [open, configuration, form, currentlyDisplayedColumns]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    if (!panelId) {
      return;
    }

    // Validate that at least dates or times are provided
    const hasDate = values.startDate || values.endDate;
    const hasTime = values.startTime || values.endTime;

    if (!hasDate && !hasTime) {
      message.error('Please select at least dates or times before submitting');
      return;
    }

    if (values.startDate && values.endDate && values.startDate.isAfter(values.endDate)) {
      message.error('Start date cannot be after end date');
      return;
    }

    if (values.startTime && values.endTime && values.startDate && values.endDate &&
        values.startDate.isSame(values.endDate, 'day') && values.startTime.isAfter(values.endTime)) {
      message.error('Start time cannot be after end time on the same day');
      return;
    }

    setLoading(true);
    try {
      const yAxisColumns = values.yAxis && values.yAxis.length > 0 ? values.yAxis : ['VatPH'];
      const selectedColumns = {
        indices: yAxisColumns.map((col: string) => availableColumns.indexOf(col)).filter((idx: number) => idx !== -1),
        headers: yAxisColumns
      };

      const newConfiguration: PLCPanelConfiguration = {
        dataRange: {
          startDate: values.startDate ? values.startDate.format('YYYY-MM-DD') : null,
          endDate: values.endDate ? values.endDate.format('YYYY-MM-DD') : null,
          startTime: values.startTime ? values.startTime.format('HH:mm') : null,
          endTime: values.endTime ? values.endTime.format('HH:mm') : null,
        },
        basic: {
          xAxisColumn: values.xAxisColumn || 'DateTime',
          selectedColumns: selectedColumns,
          group: values.group || 'None',
        },
        advanced: {
          windowMode: values.windowMode || false,
          windowConfig: configuration?.advanced?.windowConfig || undefined,
        },
        title: `Time Series - ${yAxisColumns.join(', ')}`,
        panelType: 'PLCTimeSeriesPanel' as any,
        lastModified: new Date().toISOString(),
      };

      onConfigurationSave(panelId, newConfiguration);
      onClose();

      message.success('Configuration saved!');
    } catch (error) {
      console.error('Error saving configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle drawer close
  const handleClose = () => {
    form.resetFields();
    setSelectedGroup('None');
    setWindowMode(false);
    setSelectedTargets([]);
    onClose();
  };

  // Get available columns from mock data
  const availableColumns = getAvailableColumns();
  const allColumns = getAllColumns();
  const groupingColumns = getGroupingColumns();

  // Check if data is loaded (configuration exists and has been submitted)
  const isDataLoaded = configuration &&
                      configuration.dataRange.startDate &&
                      configuration.dataRange.endDate;

  // Get distinct values for the selected grouping column
  const getDistinctGroupValues = (groupColumn: string): string[] => {
    if (!isDataLoaded || groupColumn === 'None') return [];

    const mockData = generateMockPLCData();
    const distinctValues = Array.from(new Set(mockData.map(row => (row as any)[groupColumn])));
    return distinctValues.map(val => String(val)).sort();
  };

  // Calculate pre/post options based on selected targets
  const calculatePrePostOptions = (allValues: string[], targetValues: string[]): { preOptions: number[], postOptions: number[] } => {
    if (targetValues.length === 0) return { preOptions: [], postOptions: [] };

    // Find the range of target indices
    const targetIndices = targetValues.map(val => allValues.indexOf(val)).filter(idx => idx !== -1).sort((a, b) => a - b);
    if (targetIndices.length === 0) return { preOptions: [], postOptions: [] };

    const minTargetIndex = targetIndices[0];
    const maxTargetIndex = targetIndices[targetIndices.length - 1];

    // Calculate pre options (batches before the first target)
    const preCount = minTargetIndex;
    const preOptions = preCount > 0 ? Array.from({ length: preCount }, (_, i) => i + 1) : [];

    // Calculate post options (batches after the last target)
    const postCount = allValues.length - 1 - maxTargetIndex;
    const postOptions = postCount > 0 ? Array.from({ length: postCount }, (_, i) => i + 1) : [];

    return { preOptions, postOptions };
  };

  // Handle target selection change
  const handleTargetChange = (values: string[]) => {
    setSelectedTargets(values);
    form.setFieldValue('target', values);

    // Clear pre/post selections when targets change
    form.setFieldValue('pre', undefined);
    form.setFieldValue('post', undefined);
  };

  // Handle window mode submit
  const handleWindowSubmit = async () => {
    if (!panelId || !isDataLoaded) return;

    const formValues = form.getFieldsValue();
    const { target, pre, post } = formValues;

    if (!target || target.length === 0) {
      message.error('Please select at least one target value');
      return;
    }

    setWindowLoading(true);
    try {
      // Create window mode configuration
      const windowConfig = {
        enabled: true,
        groupColumn: selectedGroup,
        target: target,
        pre: pre || 0,
        post: post || 0
      };

      const updatedConfiguration: PLCPanelConfiguration = {
        ...configuration,
        advanced: {
          ...configuration?.advanced,
          windowMode: true,
          windowConfig: windowConfig,
        },
        lastModified: new Date().toISOString(),
      };

      onConfigurationSave(panelId, updatedConfiguration);
      message.success('Window mode configuration applied!');
    } catch (error) {
      console.error('Error applying window configuration:', error);
      message.error('Failed to apply window configuration');
    } finally {
      setWindowLoading(false);
    }
  };

  // Handle window mode toggle
  const handleWindowModeChange = (e: any) => {
    const checked = e.target.checked;
    setWindowMode(checked);
    form.setFieldValue('windowMode', checked);
  };

  // Handle group selection change
  const handleGroupSelectionChange = (value: string) => {
    setSelectedGroup(value);
    form.setFieldValue('group', value);

    // Clear window mode selections when group changes
    if (value === 'None') {
      setWindowMode(false);
      form.setFieldValue('windowMode', false);
      form.setFieldValue('target', []);
      form.setFieldValue('pre', undefined);
      form.setFieldValue('post', undefined);
    }

    // Also call the original handleGroupChange to save configuration immediately
    handleGroupChange(value);
  };

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '16px',
            fontWeight: 600
          }}>
            <SettingOutlined style={{ color: '#1890ff' }} />
            Panel Configuration
          </div>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={handleClose}
            size="small"
          />
        </div>
      }
      placement="right"
      width={450}
      open={open}
      onClose={handleClose}
      closable={false}
      styles={{
        body: { padding: '0' }
      }}
    >
      <div className="plc-config-drawer">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="plc-config-form"
        >
          {/* Data Selection Section */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            margin: '16px',
            marginBottom: '20px',
            border: '1px solid #e8e8e8',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}>
            <div className="date-time-row">
              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Start Date</span>}
                name="startDate"
                style={{ flex: 1, marginRight: '8px' }}
              >
                <DatePicker
                  style={{ width: '100%', borderRadius: '8px' }}
                  placeholder="Start Date"
                  size="middle"
                />
              </Form.Item>
              <span className="arrow-separator" style={{
                color: '#1890ff',
                fontSize: '16px',
                alignSelf: 'center',
                marginTop: '24px'
              }}>→</span>
              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>End Date</span>}
                name="endDate"
                style={{ flex: 1, marginLeft: '8px' }}
              >
                <DatePicker
                  style={{ width: '100%', borderRadius: '8px' }}
                  placeholder="End Date"
                  size="middle"
                />
              </Form.Item>
            </div>

            <div className="date-time-row">
              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Start Time</span>}
                name="startTime"
                style={{ flex: 1, marginRight: '8px' }}
              >
                <TimePicker
                  style={{ width: '100%', borderRadius: '8px' }}
                  format="HH:mm"
                  placeholder="Start Time"
                  size="middle"
                />
              </Form.Item>
              <span className="arrow-separator" style={{
                color: '#1890ff',
                fontSize: '16px',
                alignSelf: 'center',
                marginTop: '24px'
              }}>→</span>
              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>End Time</span>}
                name="endTime"
                style={{ flex: 1, marginLeft: '8px' }}
              >
                <TimePicker
                  style={{ width: '100%', borderRadius: '8px' }}
                  format="HH:mm"
                  placeholder="End Time"
                  size="middle"
                />
              </Form.Item>
            </div>

            {/* Submit Button in Data Selection Zone */}
            <div style={{ marginTop: '20px' }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                style={{
                  width: '100%',
                  height: '40px',
                  fontSize: '14px',
                  fontWeight: 600,
                  borderRadius: '8px',
                  background: '#1890ff',
                  border: 'none'
                }}
                size="large"
              >
                Load Data
              </Button>
              {isDataLoaded && (
                <div style={{
                  textAlign: 'center',
                  marginTop: '8px',
                  fontSize: '12px',
                  color: '#52c41a',
                  fontWeight: 500
                }}>
                  ✅ Data loaded - Configure axes below
                </div>
              )}
            </div>
          </div>

          {/* Basic Settings */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            margin: '0 16px 20px 16px',
            border: `1px solid ${isDataLoaded ? '#e8e8e8' : '#d9d9d9'}`,
            boxShadow: isDataLoaded ? '0 2px 8px rgba(0,0,0,0.06)' : 'none',
            opacity: isDataLoaded ? 1 : 0.5
          }}>
            <div style={{
              marginBottom: '20px',
              paddingBottom: '12px',
              borderBottom: `2px solid ${isDataLoaded ? '#1890ff' : '#d9d9d9'}`
            }}>
              <Title level={5} style={{
                margin: 0,
                color: isDataLoaded ? '#1890ff' : '#999',
                fontSize: '16px',
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                ⚙️ Basic Settings
                {!isDataLoaded && <span style={{ fontSize: '12px', color: '#999', fontWeight: 400 }}>(Load data first)</span>}
              </Title>
            </div>
            <div>
              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>X Axis</span>}
                name="xAxisColumn"
                style={{ marginBottom: '16px' }}
              >
                <Select
                  placeholder="Select X Axis Column"
                  disabled={!isDataLoaded}
                  onChange={handleXAxisChange}
                  size="middle"
                  style={{
                    borderRadius: '8px',
                    width: '100%'
                  }}
                >
                  {allColumns.map(column => (
                    <Option key={column} value={column}>
                      {column}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Y Axis (Multi-select)</span>}
                name="yAxis"
                style={{ marginBottom: '16px' }}
              >
                <Select
                  mode="multiple"
                  placeholder="Select columns for Y Axis"
                  allowClear
                  disabled={!isDataLoaded}
                  onChange={handleYAxisChange}
                  size="middle"
                  style={{
                    borderRadius: '8px',
                    width: '100%'
                  }}
                  maxTagCount="responsive"
                >
                  {availableColumns.map(column => (
                    <Option key={column} value={column}>
                      {column}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Identifier</span>}
                name="group"
                style={{ marginBottom: '8px' }}
              >
                <Select
                  placeholder="None"
                  disabled={!isDataLoaded}
                  onChange={handleGroupSelectionChange}
                  size="middle"
                  style={{
                    borderRadius: '8px',
                    width: '100%'
                  }}
                >
                  <Option value="None">None</Option>
                  {groupingColumns.map(column => (
                    <Option key={column} value={column}>
                      {column}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </div>

          {/* Advanced Settings */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            margin: '0 16px 20px 16px',
            border: `1px solid ${isDataLoaded ? '#e8e8e8' : '#d9d9d9'}`,
            boxShadow: isDataLoaded ? '0 2px 8px rgba(0,0,0,0.06)' : 'none',
            opacity: isDataLoaded ? 1 : 0.5
          }}>
            <div style={{
              marginBottom: '20px',
              paddingBottom: '12px',
              borderBottom: `2px solid ${isDataLoaded ? '#fa8c16' : '#d9d9d9'}`
            }}>
              <Title level={5} style={{
                margin: 0,
                color: isDataLoaded ? '#fa8c16' : '#999',
                fontSize: '16px',
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                🔧 Advanced Settings
                {!isDataLoaded && <span style={{ fontSize: '12px', color: '#999', fontWeight: 400 }}>(Load data first)</span>}
              </Title>
            </div>
            <div>
              <Form.Item
                name="windowMode"
                valuePropName="checked"
                style={{ marginBottom: '16px' }}
              >
                <Checkbox
                  disabled={!isDataLoaded || selectedGroup === 'None'}
                  onChange={handleWindowModeChange}
                  style={{
                    fontSize: '13px',
                    color: isDataLoaded && selectedGroup !== 'None' ? '#666' : '#999'
                  }}
                >
                  Window Mode
                </Checkbox>
              </Form.Item>

              {/* Window Mode Configuration */}
              {windowMode && selectedGroup !== 'None' && (
                <div style={{
                  background: '#f6ffed',
                  borderRadius: '8px',
                  padding: '16px',
                  border: '1px solid #b7eb8f',
                  marginBottom: '16px'
                }}>
                  <div style={{
                    marginBottom: '12px',
                    fontSize: '13px',
                    fontWeight: 600,
                    color: '#52c41a'
                  }}>
                    🎯 Window Configuration
                  </div>

                  <Form.Item
                    label={<span style={{ fontSize: '12px', fontWeight: 500, color: '#666' }}>Target (Multi-select)</span>}
                    name="target"
                    style={{ marginBottom: '12px' }}
                  >
                    <Select
                      mode="multiple"
                      placeholder={`Select ${selectedGroup} values`}
                      allowClear
                      size="small"
                      style={{ borderRadius: '6px', width: '100%' }}
                      maxTagCount="responsive"
                      onChange={handleTargetChange}
                    >
                      {getDistinctGroupValues(selectedGroup).map(value => (
                        <Option key={value} value={value}>
                          {value}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <div style={{ display: 'flex', gap: '8px' }}>
                    <Form.Item
                      label={<span style={{ fontSize: '12px', fontWeight: 500, color: '#666' }}>Pre</span>}
                      name="pre"
                      style={{ flex: 1, marginBottom: '8px' }}
                    >
                      <Select
                        placeholder="Select"
                        size="small"
                        style={{ borderRadius: '6px' }}
                        disabled={selectedTargets.length === 0}
                      >
                        {calculatePrePostOptions(getDistinctGroupValues(selectedGroup), selectedTargets).preOptions.map(num => (
                          <Option key={num} value={num}>
                            {num} ({num === 1 ? 'immediate previous' : `previous ${num} combined`})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item
                      label={<span style={{ fontSize: '12px', fontWeight: 500, color: '#666' }}>Post</span>}
                      name="post"
                      style={{ flex: 1, marginBottom: '8px' }}
                    >
                      <Select
                        placeholder="Select"
                        size="small"
                        style={{ borderRadius: '6px' }}
                        disabled={selectedTargets.length === 0}
                      >
                        {calculatePrePostOptions(getDistinctGroupValues(selectedGroup), selectedTargets).postOptions.map(num => (
                          <Option key={num} value={num}>
                            {num} ({num === 1 ? 'immediate next' : `next ${num} combined`})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>

                  {/* Window Mode Submit Button */}
                  <Button
                    type="primary"
                    onClick={handleWindowSubmit}
                    loading={windowLoading}
                    style={{
                      width: '100%',
                      height: '32px',
                      fontSize: '12px',
                      fontWeight: 600,
                      borderRadius: '6px',
                      background: '#52c41a',
                      border: 'none',
                      marginTop: '8px'
                    }}
                    size="small"
                  >
                    Apply Window Settings
                  </Button>
                </div>
              )}
            </div>
          </div>
        </Form>
      </div>
    </Drawer>
  );
};

export default PLCPanelConfigDrawer;
