import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Button, Spin, Empty } from 'antd';
import { SettingOutlined, LineChartOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import {
  PLCTimeSeriesPanelProps
} from '../types/PLCTypes';
import {
  generateMockPLCData,
  transformDataForECharts
} from '../data/mockPLCData';

const PLCTimeSeriesPanel: React.FC<PLCTimeSeriesPanelProps> = ({
  configuration,
  selectedColumns = { indices: [], headers: [] },
  isFullScreen = false,
  onOpenConfiguration
}) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);
  const [chartUpdateTrigger, setChartUpdateTrigger] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const echartsRef = useRef<any>(null);

  // Check if panel has been configured and submitted - simplified check
  const isConfigured = configuration &&
                      configuration.dataRange.startDate &&
                      configuration.dataRange.endDate;

  // Always show data when configured, even if no Y-axis columns selected yet

  // Always load mock data for display
  const mockData = generateMockPLCData();
  const availableColumns = ['VatPH', 'RecipeNo', 'CookingTime', 'Temperature', 'Pressure', 'FlowRate'];

  // Always show data with default column selection
  // If configured and has Y-axis columns, use those; otherwise default to VatPH
  const columnsToDisplay = (configuration?.basic?.selectedColumns?.headers &&
                           configuration.basic.selectedColumns.headers.length > 0)
    ? configuration.basic.selectedColumns.headers
    : ['VatPH']; // Always default to VatPH

  // Create selectedColumns state that matches the batch exploration pattern
  const effectiveSelectedColumns = {
    indices: columnsToDisplay.map(col => availableColumns.indexOf(col)).filter(idx => idx !== -1),
    headers: columnsToDisplay
  };

  // Debug logging (remove in production)
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 PLCTimeSeriesPanel - Configuration:', configuration);
    console.log('🔍 PLCTimeSeriesPanel - isConfigured:', isConfigured);
    console.log('🔍 PLCTimeSeriesPanel - columnsToDisplay:', columnsToDisplay);
    console.log('🔍 PLCTimeSeriesPanel - effectiveSelectedColumns:', effectiveSelectedColumns);
    console.log('🔍 PLCTimeSeriesPanel - Y-axis from config:', configuration?.basic?.selectedColumns?.headers);
  }

  // Monitor container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setContainerSize({ width: offsetWidth, height: offsetHeight });
        setIsChartReady(offsetWidth > 0 && offsetHeight > 0);
      }
    };

    updateSize();
    
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Handle configuration button click
  const handleConfigureClick = () => {
    if (onOpenConfiguration) {
      // Pass currently displayed columns to the configuration
      onOpenConfiguration(columnsToDisplay);
    }
  };

  // Always show stacked view - no tabs needed
  const shouldShowStackedView = true; // Always use stacked view
  const displayedColumnsCount = effectiveSelectedColumns.headers.length;

  // Additional debug logging (remove in production)
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 PLCTimeSeriesPanel - Should show stacked view:', shouldShowStackedView);
    console.log('🔍 PLCTimeSeriesPanel - X-axis column:', configuration?.basic?.xAxisColumn || 'DateTime');
  }

  // Force chart re-render when configuration changes
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 PLCTimeSeriesPanel - Chart update triggered due to configuration change');
      console.log('🔄 PLCTimeSeriesPanel - Columns to display changed:', columnsToDisplay);
      console.log('🔄 PLCTimeSeriesPanel - Configuration changed:', configuration);
    }

    // Clear the chart instance completely to prevent grid/axis misalignment
    if (echartsRef.current) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      if (chartInstance) {
        chartInstance.dispose();
      }
    }

    // Trigger re-render with optimized delay
    const timeoutId = setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 50); // Reduced delay for better performance

    return () => clearTimeout(timeoutId);
  }, [columnsToDisplay.join(','), configuration?.basic?.group, configuration?.basic?.selectedColumns?.headers?.join(','), configuration?.basic?.xAxisColumn]);

  // All old chart creation functions removed - using transformDataForECharts instead

  // Create plot data using transformDataForECharts with window mode support
  const plotData = useMemo(() => {
    const xAxisColumn = configuration?.basic?.xAxisColumn || 'DateTime';
    const groupByColumn = configuration?.basic?.group && configuration.basic.group !== 'None'
      ? configuration.basic.group
      : null;

    const windowConfig = configuration?.advanced?.windowConfig;

    // Use transformDataForECharts for consistent chart generation
    const chartOption = transformDataForECharts(
      mockData,
      xAxisColumn,
      effectiveSelectedColumns.headers,
      groupByColumn || undefined,
      windowConfig
    );

    if (!chartOption || !chartOption.series) {
      return [];
    }

    // Convert ECharts series format to our internal format for compatibility
    return chartOption.series.map((series: any) => ({
      name: series.name,
      data: series.data || [],
      groupValue: null, // Not needed for new format
      originalColumn: series.name.split(' - ')[0] || series.name.split(' (')[0] || series.name,
      lineStyle: series.lineStyle,
      symbolSize: series.symbolSize
    }));
  }, [effectiveSelectedColumns.headers, mockData, configuration?.basic?.xAxisColumn, configuration?.basic?.group, configuration?.advanced?.windowConfig]);

  // Create true stacked ECharts option like batch exploration
  const createStackedOption = useMemo(() => {
    if (!plotData || plotData.length === 0) {
      return {
        series: [],
        xAxis: { type: 'time' },
        yAxis: { type: 'value' },
        hasData: false
      };
    }

    const colors = [
      '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
    ];

    // Get X-axis column and determine its type
    const xAxisColumn = configuration?.basic?.xAxisColumn || 'DateTime';
    const xAxisType = xAxisColumn === 'DateTime' ? 'time' : 'value';

    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Creating true stacked chart with:', plotData.map(p => p.name));
      console.log('📊 Plot data length:', plotData.length);
      console.log('📊 X-axis column:', xAxisColumn, 'Type:', xAxisType);
    }

    return {
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: plotData.map(s => s.name),
        top: isFullScreen ? '0%' : 30,
        type: 'scroll',
        ...(isFullScreen ? {
          itemGap: 8,
          textStyle: { fontSize: 11 }
        } : {})
      },
      grid: (() => {
        // Determine unique Y-axis columns for grid layout
        const uniqueColumns = effectiveSelectedColumns.headers;
        const gridCount = uniqueColumns.length;

        return uniqueColumns.map((_, index) => {
          // Create separate grid for each unique column (stacked vertically)
          const leftMargin = isFullScreen ? '1%' : '10px';
          const rightMargin = isFullScreen ? '2%' : '10px';
          const topBase = isFullScreen ? 2 : 15;
          const heightBase = isFullScreen ? 96 : 75;
          const bottomMargin = isFullScreen ? 2 : 10;

          return {
            left: leftMargin,
            right: rightMargin,
            top: `${topBase + index * (heightBase / gridCount)}%`,
            height: `${(heightBase - bottomMargin) / gridCount}%`,
            containLabel: true
          };
        });
      })(),
      xAxis: effectiveSelectedColumns.headers.map((_, index) => ({
        type: xAxisType,
        gridIndex: index,
        boundaryGap: false,
        name: xAxisColumn,
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#333',
            width: 1
          }
        },
        axisTick: {
          show: true,
          length: 5
        },
        axisLabel: {
          show: true,
          fontSize: 11,
          color: '#666',
          ...(xAxisType === 'time' ? {
            formatter: function (value: any) {
              return new Date(value).toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
              });
            }
          } : {
            formatter: function (value: any) {
              return typeof value === 'number' ? value.toFixed(2) : value;
            }
          })
        },
        show: index === effectiveSelectedColumns.headers.length - 1 // Only show x-axis on bottom chart
      })),
      yAxis: effectiveSelectedColumns.headers.map((column, index) => ({
        type: 'value',
        gridIndex: index,
        name: column,
        nameLocation: 'middle',
        nameGap: 60,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold',
          color: colors[index % colors.length]
        },
        scale: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#333',
            width: 1
          }
        },
        axisTick: {
          show: true,
          length: 5
        },
        axisLabel: {
          show: true,
          fontSize: 11,
          color: '#666',
          formatter: function (value: any) {
            return typeof value === 'number' ? value.toFixed(2) : value;
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#e6e6e6',
            width: 1,
            type: 'dashed'
          }
        },
        splitArea: {
          show: false // Disable split area for cleaner look
        }
      })),
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: effectiveSelectedColumns.headers.map((_, i) => i),
          filterMode: 'filter'
        },
        {
          type: 'slider',
          xAxisIndex: effectiveSelectedColumns.headers.map((_, i) => i),
          filterMode: 'filter'
        }
      ],
      series: plotData.map((s, index) => {
        // Find which Y-axis column this series belongs to
        const originalColumn = s.originalColumn || s.name.split(' (')[0]; // Handle grouped names
        const columnIndex = effectiveSelectedColumns.headers.indexOf(originalColumn);
        const gridIndex = columnIndex >= 0 ? columnIndex : 0;

        return {
          name: s.name,
          type: 'line',
          xAxisIndex: gridIndex,
          yAxisIndex: gridIndex,
          data: s.data || [],
          smooth: false,
          symbol: 'circle',
          symbolSize: 3, // Small but visible dots
          showSymbol: true, // Show small dots for better visibility
          lineStyle: {
            width: 2, // Slightly thinner lines
            color: colors[index % colors.length]
          },
          itemStyle: {
            color: colors[index % colors.length]
          },
          emphasis: {
            focus: 'series',
            lineStyle: {
              width: 3 // Slightly thicker on hover
            },
            itemStyle: {
              borderWidth: 1
            }
          }
          // Removed markPoint (min/max markers) for cleaner look
        };
      })
    };
  }, [plotData, isFullScreen]);

  // Removed unused tab functions since we're using direct chart rendering

  // Render empty state with configuration prompt when panel is not configured
  if (!configuration || !isConfigured) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<LineChartOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-gray-700 mb-2">
                {!configuration ? 'Panel Not Configured' : 'Configuration Incomplete'}
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {!configuration
                  ? 'Configure this panel to display PLC time series data'
                  : 'Please complete the configuration by selecting date range and columns, then click Submit'
                }
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
              >
                Configure Panel
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // No longer needed since we're not using tabs

  // Always render stacked view - no tabs needed
  const xAxisColumn = configuration?.basic?.xAxisColumn || 'DateTime';

  return (
    <div className={`time-series-panel ${isFullScreen ? 'h-full' : 'h-[95%]'}`} ref={containerRef} style={{ position: 'relative' }}>
      <div className={`flex justify-between items-center ${isFullScreen ? 'mb-0 px-2 pt-0' : 'mb-1 px-3 pt-1'}`}>
        <h3 className="text-base font-medium">
          Time Series ({displayedColumnsCount}) - X-axis: {xAxisColumn}
        </h3>
      </div>
      <div className={`${isFullScreen ? 'p-0 h-[calc(100%-100px)]' : 'p-2 h-[calc(100%-30px)]'} overflow-auto`}>
        <div style={{
          height: '100%',
          minHeight: isFullScreen ? '100%' : `${Math.max(400, displayedColumnsCount * 150)}px`
        }}>
          {containerSize.width > 0 && containerSize.height > 0 && isChartReady ? (
            <ReactECharts
              key={`stacked-${chartUpdateTrigger}`}
              ref={echartsRef}
              option={createStackedOption}
              style={{
                width: '100%',
                height: '100%',
                ...(isFullScreen ? {} : { minHeight: '400px' })
              }}
              opts={{ renderer: 'canvas' }}
              notMerge={true}
            />
          ) : (
            <div style={{
              width: '100%',
              height: isFullScreen ? '100%' : '400px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Spin size="large" tip="Loading chart..." />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PLCTimeSeriesPanel;
