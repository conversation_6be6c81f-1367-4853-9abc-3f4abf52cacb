// Optimized Mock PLC data for time series visualization with synchronized time brackets

export interface MockPLCDataRow {
  DateTime: string;
  BatchId: string;
  VatPH: number;
  RecipeNo: number;
  CookingTime: number;
  Temperature: number;
  Pressure: number;
  FlowRate: number;
}

// Mock data with same time brackets for all batches but different values
const mockPLCDataArray: MockPLCDataRow[] = [
  // Time: 10:00 - All batches start
  { DateTime: "2024-01-01T10:00:00.000Z", BatchId: "BATCH_001", VatPH: 6.5, RecipeNo: 101, CookingTime: 0, Temperature: 20, Pressure: 1.0, FlowRate: 15.0 },
  { DateTime: "2024-01-01T10:00:00.000Z", BatchId: "BATCH_002", VatPH: 6.8, RecipeNo: 102, CookingTime: 0, Temperature: 22, Pressure: 1.1, FlowRate: 16.0 },
  { DateTime: "2024-01-01T10:00:00.000Z", BatchId: "BATCH_003", VatPH: 6.2, RecipeNo: 103, CookingTime: 0, Temperature: 18, Pressure: 0.9, FlowRate: 14.0 },

  // Time: 10:05 - 5 minutes in
  { DateTime: "2024-01-01T10:05:00.000Z", BatchId: "BATCH_001", VatPH: 6.6, RecipeNo: 101, CookingTime: 5, Temperature: 35, Pressure: 1.2, FlowRate: 16.5 },
  { DateTime: "2024-01-01T10:05:00.000Z", BatchId: "BATCH_002", VatPH: 6.9, RecipeNo: 102, CookingTime: 5, Temperature: 38, Pressure: 1.3, FlowRate: 17.2 },
  { DateTime: "2024-01-01T10:05:00.000Z", BatchId: "BATCH_003", VatPH: 6.3, RecipeNo: 103, CookingTime: 5, Temperature: 32, Pressure: 1.0, FlowRate: 15.8 },

  // Time: 10:10 - 10 minutes in
  { DateTime: "2024-01-01T10:10:00.000Z", BatchId: "BATCH_001", VatPH: 6.7, RecipeNo: 101, CookingTime: 10, Temperature: 50, Pressure: 1.5, FlowRate: 18.0 },
  { DateTime: "2024-01-01T10:10:00.000Z", BatchId: "BATCH_002", VatPH: 7.0, RecipeNo: 102, CookingTime: 10, Temperature: 55, Pressure: 1.6, FlowRate: 19.0 },
  { DateTime: "2024-01-01T10:10:00.000Z", BatchId: "BATCH_003", VatPH: 6.4, RecipeNo: 103, CookingTime: 10, Temperature: 45, Pressure: 1.3, FlowRate: 17.0 },

  // Time: 10:15 - 15 minutes in
  { DateTime: "2024-01-01T10:15:00.000Z", BatchId: "BATCH_001", VatPH: 6.9, RecipeNo: 101, CookingTime: 15, Temperature: 65, Pressure: 1.8, FlowRate: 20.0 },
  { DateTime: "2024-01-01T10:15:00.000Z", BatchId: "BATCH_002", VatPH: 7.2, RecipeNo: 102, CookingTime: 15, Temperature: 72, Pressure: 2.0, FlowRate: 21.5 },
  { DateTime: "2024-01-01T10:15:00.000Z", BatchId: "BATCH_003", VatPH: 6.6, RecipeNo: 103, CookingTime: 15, Temperature: 58, Pressure: 1.6, FlowRate: 18.5 },

  // Time: 10:20 - 20 minutes in (peak)
  { DateTime: "2024-01-01T10:20:00.000Z", BatchId: "BATCH_001", VatPH: 7.1, RecipeNo: 101, CookingTime: 20, Temperature: 75, Pressure: 2.1, FlowRate: 22.0 },
  { DateTime: "2024-01-01T10:20:00.000Z", BatchId: "BATCH_002", VatPH: 7.4, RecipeNo: 102, CookingTime: 20, Temperature: 85, Pressure: 2.3, FlowRate: 24.0 },
  { DateTime: "2024-01-01T10:20:00.000Z", BatchId: "BATCH_003", VatPH: 6.8, RecipeNo: 103, CookingTime: 20, Temperature: 68, Pressure: 1.9, FlowRate: 20.0 },

  // Time: 10:25 - 25 minutes in (cooling down)
  { DateTime: "2024-01-01T10:25:00.000Z", BatchId: "BATCH_001", VatPH: 7.0, RecipeNo: 101, CookingTime: 25, Temperature: 70, Pressure: 2.0, FlowRate: 21.0 },
  { DateTime: "2024-01-01T10:25:00.000Z", BatchId: "BATCH_002", VatPH: 7.3, RecipeNo: 102, CookingTime: 25, Temperature: 80, Pressure: 2.2, FlowRate: 23.0 },
  { DateTime: "2024-01-01T10:25:00.000Z", BatchId: "BATCH_003", VatPH: 6.7, RecipeNo: 103, CookingTime: 25, Temperature: 63, Pressure: 1.8, FlowRate: 19.0 },

  // Time: 10:30 - 30 minutes in (end)
  { DateTime: "2024-01-01T10:30:00.000Z", BatchId: "BATCH_001", VatPH: 6.8, RecipeNo: 101, CookingTime: 30, Temperature: 60, Pressure: 1.7, FlowRate: 19.0 },
  { DateTime: "2024-01-01T10:30:00.000Z", BatchId: "BATCH_002", VatPH: 7.1, RecipeNo: 102, CookingTime: 30, Temperature: 70, Pressure: 1.9, FlowRate: 21.0 },
  { DateTime: "2024-01-01T10:30:00.000Z", BatchId: "BATCH_003", VatPH: 6.5, RecipeNo: 103, CookingTime: 30, Temperature: 55, Pressure: 1.5, FlowRate: 17.5 }
];

// Cache for loaded data to improve performance
let cachedMockData: MockPLCDataRow[] | null = null;

// Load mock data
export const loadMockPLCData = async (): Promise<MockPLCDataRow[]> => {
  // Return cached data if available
  if (cachedMockData) {
    return cachedMockData;
  }

  cachedMockData = mockPLCDataArray;
  return cachedMockData;
};

// Synchronous version for backward compatibility
export const generateMockPLCData = (): MockPLCDataRow[] => {
  // Return cached data if available, otherwise return data directly
  if (cachedMockData) {
    return cachedMockData;
  }

  cachedMockData = mockPLCDataArray;
  return cachedMockData;
};



// Get available columns for Y-axis selection (excluding DateTime which is used for X-axis)
export const getAvailableColumns = (): string[] => {
  return ['VatPH', 'RecipeNo', 'CookingTime', 'Temperature', 'Pressure', 'FlowRate'];
};

// Get all columns including DateTime for X-axis selection
export const getAllColumns = (): string[] => {
  return ['DateTime', 'VatPH', 'RecipeNo', 'CookingTime', 'Temperature', 'Pressure', 'FlowRate', 'BatchId'];
};

// Get available batches for grouping
export const getAvailableBatches = (): string[] => {
  return ['BATCH_001', 'BATCH_002', 'BATCH_003', 'BATCH_004'];
};

// Get available grouping columns (columns that make sense for grouping)
export const getGroupingColumns = (): string[] => {
  return ['BatchId', 'RecipeNo'];
};

// Window mode configuration interface
export interface WindowModeConfig {
  enabled: boolean;
  groupColumn: string;
  target: string[];
  pre: number;
  post: number;
}

// Transform data for ECharts based on configuration with dynamic X-axis and grouping
export const transformDataForECharts = (
  data: MockPLCDataRow[],
  xAxisColumn: string,
  yAxisColumns: string[],
  groupByColumn?: string,
  windowConfig?: WindowModeConfig
) => {
  if (yAxisColumns.length === 0) {
    return null;
  }



  const series: any[] = [];

  // Handle window mode
  if (windowConfig && windowConfig.enabled && groupByColumn && groupByColumn !== 'None') {
    const allGroupValues = Array.from(new Set(data.map(row => (row as any)[groupByColumn]))).sort();
    const { target, pre, post } = windowConfig;

    // Find target indices
    const targetIndices = target.map(val => allGroupValues.indexOf(val)).filter(idx => idx !== -1).sort((a, b) => a - b);
    if (targetIndices.length === 0) return null;

    const minTargetIndex = targetIndices[0];
    const maxTargetIndex = targetIndices[targetIndices.length - 1];

    yAxisColumns.forEach(yColumn => {
      // Target series (combined)
      const targetData = data.filter(row => target.includes((row as any)[groupByColumn]));
      if (targetData.length > 0) {
        // Group target data by time and average values
        const targetGrouped = targetData.reduce((acc: any, row) => {
          const timeKey = (row as any)[xAxisColumn];
          if (!acc[timeKey]) {
            acc[timeKey] = { sum: 0, count: 0 };
          }
          acc[timeKey].sum += (row as any)[yColumn];
          acc[timeKey].count += 1;
          return acc;
        }, {});

        const targetSeriesData = Object.keys(targetGrouped).map(timeKey => [
          timeKey,
          targetGrouped[timeKey].sum / targetGrouped[timeKey].count
        ]);

        series.push({
          name: `${yColumn} - Target (${target.join(', ')})`,
          type: 'line',
          data: targetSeriesData,
          smooth: false,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: { width: 3, color: '#1890ff' }
        });
      }

      // Pre series
      if (pre > 0 && minTargetIndex > 0) {
        const preStartIndex = Math.max(0, minTargetIndex - pre);
        const preValues = allGroupValues.slice(preStartIndex, minTargetIndex);
        const preData = data.filter(row => preValues.includes((row as any)[groupByColumn]));

        if (preData.length > 0) {
          const preGrouped = preData.reduce((acc: any, row) => {
            const timeKey = (row as any)[xAxisColumn];
            if (!acc[timeKey]) {
              acc[timeKey] = { sum: 0, count: 0 };
            }
            acc[timeKey].sum += (row as any)[yColumn];
            acc[timeKey].count += 1;
            return acc;
          }, {});

          const preSeriesData = Object.keys(preGrouped).map(timeKey => [
            timeKey,
            preGrouped[timeKey].sum / preGrouped[timeKey].count
          ]);

          series.push({
            name: `${yColumn} - Pre ${pre} (${preValues.join(', ')})`,
            type: 'line',
            data: preSeriesData,
            smooth: false,
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: { width: 2, color: '#52c41a' }
          });
        }
      }

      // Post series
      if (post > 0 && maxTargetIndex < allGroupValues.length - 1) {
        const postEndIndex = Math.min(allGroupValues.length, maxTargetIndex + 1 + post);
        const postValues = allGroupValues.slice(maxTargetIndex + 1, postEndIndex);
        const postData = data.filter(row => postValues.includes((row as any)[groupByColumn]));

        if (postData.length > 0) {
          const postGrouped = postData.reduce((acc: any, row) => {
            const timeKey = (row as any)[xAxisColumn];
            if (!acc[timeKey]) {
              acc[timeKey] = { sum: 0, count: 0 };
            }
            acc[timeKey].sum += (row as any)[yColumn];
            acc[timeKey].count += 1;
            return acc;
          }, {});

          const postSeriesData = Object.keys(postGrouped).map(timeKey => [
            timeKey,
            postGrouped[timeKey].sum / postGrouped[timeKey].count
          ]);

          series.push({
            name: `${yColumn} - Post ${post} (${postValues.join(', ')})`,
            type: 'line',
            data: postSeriesData,
            smooth: false,
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: { width: 2, color: '#fa8c16' }
          });
        }
      }
    });
  } else if (groupByColumn && groupByColumn !== 'None') {
    // Regular grouping mode
    const uniqueValues = new Set(data.map(row => (row as any)[groupByColumn]));
    const groupValues = Array.from(uniqueValues);

    yAxisColumns.forEach(yColumn => {
      groupValues.forEach(groupValue => {
        const groupData = data.filter(row => (row as any)[groupByColumn] === groupValue);
        series.push({
          name: `${yColumn} - ${groupValue}`,
          type: 'line',
          data: groupData.map(row => [(row as any)[xAxisColumn], (row as any)[yColumn]]),
          smooth: false,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: { width: 2 }
        });
      });
    });
  } else {
    // No grouping - simple series
    yAxisColumns.forEach(yColumn => {
      series.push({
        name: yColumn,
        type: 'line',
        data: data.map(row => [(row as any)[xAxisColumn], (row as any)[yColumn]]),
        smooth: false,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: { width: 2 }
      });
    });
  }

  // Determine X-axis type based on column
  const xAxisType = xAxisColumn === 'DateTime' ? 'time' : 'value';



  return {
    animation: false,
    title: {
      text: `${yAxisColumns.join(', ')} vs ${xAxisColumn}`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: series.map(s => s.name),
      bottom: 10,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '20%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: xAxisType,
      name: xAxisColumn,
      nameLocation: 'middle',
      nameGap: 30,
      ...(xAxisType === 'time' ? {
        axisLabel: {
          formatter: function (value: any) {
            return new Date(value).toLocaleTimeString('en-US', {
              hour12: false,
              hour: '2-digit',
              minute: '2-digit'
            });
          }
        }
      } : {})
    },
    yAxis: {
      type: 'value',
      name: yAxisColumns.length === 1 ? yAxisColumns[0] : 'Value',
      nameLocation: 'middle',
      nameGap: 50
    },
    series
  };
};

// Generate individual column data for single column display
export const generateColumnTabData = (data: MockPLCDataRow[], xAxisColumn: string, yAxisColumn: string) => {
  const xAxisType = xAxisColumn === 'DateTime' ? 'time' : 'value';

  return {
    animation: false,
    title: {
      text: `${yAxisColumn} vs ${xAxisColumn}`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: xAxisType,
      name: xAxisColumn,
      nameLocation: 'middle',
      nameGap: 30,
      ...(xAxisType === 'time' ? {
        axisLabel: {
          formatter: function (value: any) {
            return new Date(value).toLocaleTimeString('en-US', {
              hour12: false,
              hour: '2-digit',
              minute: '2-digit'
            });
          }
        }
      } : {})
    },
    yAxis: {
      type: 'value',
      name: yAxisColumn,
      nameLocation: 'middle',
      nameGap: 50
    },
    series: [{
      name: yAxisColumn,
      type: 'line',
      data: data.map(row => [(row as any)[xAxisColumn], (row as any)[yAxisColumn]]),
      smooth: false,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: { width: 2 },
      areaStyle: { opacity: 0.1 }
    }]
  };
};
